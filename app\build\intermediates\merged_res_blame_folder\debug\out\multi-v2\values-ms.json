{"logs": [{"outputFile": "com.rootdetectionblocker.app-mergeDebugResources-27:/values-ms/values-ms.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\730d661b65f3ba2f8a248144c0a9483c\\transformed\\appcompat-1.6.1\\res\\values-ms\\values-ms.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,216,321,429,516,620,731,810,888,979,1072,1167,1261,1359,1452,1547,1641,1732,1823,1903,2015,2123,2220,2329,2433,2540,2699,2800", "endColumns": "110,104,107,86,103,110,78,77,90,92,94,93,97,92,94,93,90,90,79,111,107,96,108,103,106,158,100,80", "endOffsets": "211,316,424,511,615,726,805,883,974,1067,1162,1256,1354,1447,1542,1636,1727,1818,1898,2010,2118,2215,2324,2428,2535,2694,2795,2876"}, "to": {"startLines": "6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,102", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "329,440,545,653,740,844,955,1034,1112,1203,1296,1391,1485,1583,1676,1771,1865,1956,2047,2127,2239,2347,2444,2553,2657,2764,2923,8655", "endColumns": "110,104,107,86,103,110,78,77,90,92,94,93,97,92,94,93,90,90,79,111,107,96,108,103,106,158,100,80", "endOffsets": "435,540,648,735,839,950,1029,1107,1198,1291,1386,1480,1578,1671,1766,1860,1951,2042,2122,2234,2342,2439,2548,2652,2759,2918,3019,8731"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\cb397eff35725b147c51b001dccff3a5\\transformed\\core-1.10.1\\res\\values-ms\\values-ms.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,150,252,349,459,565,683,798", "endColumns": "94,101,96,109,105,117,114,100", "endOffsets": "145,247,344,454,560,678,793,894"}, "to": {"startLines": "38,39,40,41,42,43,44,103", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3449,3544,3646,3743,3853,3959,4077,8736", "endColumns": "94,101,96,109,105,117,114,100", "endOffsets": "3539,3641,3738,3848,3954,4072,4187,8832"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\7f7393ba31e25e2f4376a79e2eb9d0b4\\transformed\\material-1.9.0\\res\\values-ms\\values-ms.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,279,359,438,525,617,704,807,923,1006,1071,1164,1229,1288,1375,1437,1499,1559,1625,1687,1741,1849,1906,1967,2022,2093,2213,2304,2390,2538,2624,2710,2798,2851,2902,2968,3039,3117,3200,3273,3349,3422,3493,3585,3658,3748,3841,3915,3986,4077,4129,4197,4281,4366,4428,4492,4555,4659,4765,4861,4969,5026,5081", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67", "endColumns": "12,79,78,86,91,86,102,115,82,64,92,64,58,86,61,61,59,65,61,53,107,56,60,54,70,119,90,85,147,85,85,87,52,50,65,70,77,82,72,75,72,70,91,72,89,92,73,70,90,51,67,83,84,61,63,62,103,105,95,107,56,54,85", "endOffsets": "274,354,433,520,612,699,802,918,1001,1066,1159,1224,1283,1370,1432,1494,1554,1620,1682,1736,1844,1901,1962,2017,2088,2208,2299,2385,2533,2619,2705,2793,2846,2897,2963,3034,3112,3195,3268,3344,3417,3488,3580,3653,3743,3836,3910,3981,4072,4124,4192,4276,4361,4423,4487,4550,4654,4760,4856,4964,5021,5076,5162"}, "to": {"startLines": "2,33,34,35,36,37,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,3024,3104,3183,3270,3362,4192,4295,4411,4494,4559,4652,4717,4776,4863,4925,4987,5047,5113,5175,5229,5337,5394,5455,5510,5581,5701,5792,5878,6026,6112,6198,6286,6339,6390,6456,6527,6605,6688,6761,6837,6910,6981,7073,7146,7236,7329,7403,7474,7565,7617,7685,7769,7854,7916,7980,8043,8147,8253,8349,8457,8514,8569", "endLines": "5,33,34,35,36,37,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101", "endColumns": "12,79,78,86,91,86,102,115,82,64,92,64,58,86,61,61,59,65,61,53,107,56,60,54,70,119,90,85,147,85,85,87,52,50,65,70,77,82,72,75,72,70,91,72,89,92,73,70,90,51,67,83,84,61,63,62,103,105,95,107,56,54,85", "endOffsets": "324,3099,3178,3265,3357,3444,4290,4406,4489,4554,4647,4712,4771,4858,4920,4982,5042,5108,5170,5224,5332,5389,5450,5505,5576,5696,5787,5873,6021,6107,6193,6281,6334,6385,6451,6522,6600,6683,6756,6832,6905,6976,7068,7141,7231,7324,7398,7469,7560,7612,7680,7764,7849,7911,7975,8038,8142,8248,8344,8452,8509,8564,8650"}}]}]}
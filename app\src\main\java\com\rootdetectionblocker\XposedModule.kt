package com.rootdetectionblocker

import android.app.AlertDialog
import android.app.Dialog
import android.content.Context
import android.view.View
import android.view.ViewGroup
import android.widget.TextView
import de.robv.android.xposed.IXposedHookLoadPackage
import de.robv.android.xposed.XC_MethodHook
import de.robv.android.xposed.XposedBridge
import de.robv.android.xposed.XposedHelpers
import de.robv.android.xposed.callbacks.XC_LoadPackage

class XposedModule : IXposedHookLoadPackage {

    companion object {
        private const val TAG = "RootDetectionBlocker"
        private val TARGET_WORDS = arrayOf("rooting", "detected")
    }

    override fun handleLoadPackage(lpparam: XC_LoadPackage.LoadPackageParam) {
        // Skip system apps and our own module
        if (lpparam.packageName.startsWith("android.") || 
            lpparam.packageName.startsWith("com.android.") ||
            lpparam.packageName == "com.rootdetectionblocker") {
            return
        }

        try {
            hookDialogCreation(lpparam)
            hookAlertDialogCreation(lpparam)
            hookAlertDialogBuilderShow(lpparam)
        } catch (e: Exception) {
            XposedBridge.log("$TAG: Error hooking package ${lpparam.packageName}: ${e.message}")
        }
    }

    private fun hookDialogCreation(lpparam: XC_LoadPackage.LoadPackageParam) {
        try {
            val dialogClass = XposedHelpers.findClass("android.app.Dialog", lpparam.classLoader)
            
            XposedHelpers.findAndHookMethod(dialogClass, "show", object : XC_MethodHook() {
                override fun beforeHookedMethod(param: MethodHookParam) {
                    try {
                        val dialog = param.thisObject as Dialog
                        if (shouldBlockDialog(dialog)) {
                            XposedBridge.log("$TAG: Blocked root detection dialog in ${lpparam.packageName}")
                            param.result = null // Prevent the dialog from showing
                        }
                    } catch (e: Exception) {
                        XposedBridge.log("$TAG: Error in Dialog.show hook: ${e.message}")
                    }
                }
            })
        } catch (e: Exception) {
            XposedBridge.log("$TAG: Failed to hook Dialog.show: ${e.message}")
        }
    }

    private fun hookAlertDialogCreation(lpparam: XC_LoadPackage.LoadPackageParam) {
        try {
            val alertDialogClass = XposedHelpers.findClass("android.app.AlertDialog", lpparam.classLoader)
            
            XposedHelpers.findAndHookMethod(alertDialogClass, "show", object : XC_MethodHook() {
                override fun beforeHookedMethod(param: MethodHookParam) {
                    try {
                        val dialog = param.thisObject as AlertDialog
                        if (shouldBlockDialog(dialog)) {
                            XposedBridge.log("$TAG: Blocked root detection AlertDialog in ${lpparam.packageName}")
                            param.result = null // Prevent the dialog from showing
                        }
                    } catch (e: Exception) {
                        XposedBridge.log("$TAG: Error in AlertDialog.show hook: ${e.message}")
                    }
                }
            })
        } catch (e: Exception) {
            XposedBridge.log("$TAG: Failed to hook AlertDialog.show: ${e.message}")
        }
    }

    private fun hookAlertDialogBuilderShow(lpparam: XC_LoadPackage.LoadPackageParam) {
        try {
            val builderClass = XposedHelpers.findClass("android.app.AlertDialog\$Builder", lpparam.classLoader)
            
            XposedHelpers.findAndHookMethod(builderClass, "show", object : XC_MethodHook() {
                override fun afterHookedMethod(param: MethodHookParam) {
                    try {
                        val dialog = param.result as? AlertDialog
                        if (dialog != null && shouldBlockDialog(dialog)) {
                            XposedBridge.log("$TAG: Blocked root detection AlertDialog.Builder in ${lpparam.packageName}")
                            dialog.dismiss() // Dismiss immediately after creation
                        }
                    } catch (e: Exception) {
                        XposedBridge.log("$TAG: Error in AlertDialog.Builder.show hook: ${e.message}")
                    }
                }
            })
        } catch (e: Exception) {
            XposedBridge.log("$TAG: Failed to hook AlertDialog.Builder.show: ${e.message}")
        }
    }

    private fun shouldBlockDialog(dialog: Dialog): Boolean {
        return try {
            val dialogContent = extractDialogText(dialog)
            val contentLower = dialogContent.lowercase()
            
            val hasRooting = TARGET_WORDS[0] in contentLower
            val hasDetected = TARGET_WORDS[1] in contentLower
            
            if (hasRooting && hasDetected) {
                XposedBridge.log("$TAG: Found target words in dialog: $dialogContent")
                true
            } else {
                false
            }
        } catch (e: Exception) {
            XposedBridge.log("$TAG: Error checking dialog content: ${e.message}")
            false
        }
    }

    private fun extractDialogText(dialog: Dialog): String {
        val textBuilder = StringBuilder()
        
        try {
            // Get the dialog's content view
            val window = dialog.window
            val decorView = window?.decorView
            
            if (decorView != null) {
                extractTextFromView(decorView, textBuilder)
            }
            
            // Also try to get text from AlertDialog specific methods
            if (dialog is AlertDialog) {
                try {
                    val messageField = AlertDialog::class.java.getDeclaredField("mAlert")
                    messageField.isAccessible = true
                    val alertController = messageField.get(dialog)
                    
                    val messageViewField = alertController::class.java.getDeclaredField("mMessageView")
                    messageViewField.isAccessible = true
                    val messageView = messageViewField.get(alertController) as? TextView
                    
                    messageView?.text?.let { textBuilder.append(" ").append(it) }
                } catch (e: Exception) {
                    // Fallback - ignore reflection errors
                }
            }
            
        } catch (e: Exception) {
            XposedBridge.log("$TAG: Error extracting dialog text: ${e.message}")
        }
        
        return textBuilder.toString()
    }

    private fun extractTextFromView(view: View, textBuilder: StringBuilder) {
        try {
            when (view) {
                is TextView -> {
                    view.text?.let { textBuilder.append(" ").append(it) }
                }
                is ViewGroup -> {
                    for (i in 0 until view.childCount) {
                        extractTextFromView(view.getChildAt(i), textBuilder)
                    }
                }
            }
        } catch (e: Exception) {
            // Ignore individual view extraction errors
        }
    }
}

1<?xml version="1.0" encoding="utf-8"?>
2<manifest xmlns:android="http://schemas.android.com/apk/res/android"
3    package="com.rootdetectionblocker"
4    android:versionCode="1"
5    android:versionName="1.0" >
6
7    <uses-sdk
8        android:minSdkVersion="21"
8-->D:\CData\Dsktp\lsposed_app\app\src\main\AndroidManifest.xml
9        android:targetSdkVersion="34" />
9-->D:\CData\Dsktp\lsposed_app\app\src\main\AndroidManifest.xml
10
11    <permission
11-->[androidx.core:core:1.10.1] C:\Users\<USER>\.gradle\caches\transforms-3\cb397eff35725b147c51b001dccff3a5\transformed\core-1.10.1\AndroidManifest.xml:22:5-24:47
12        android:name="com.rootdetectionblocker.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION"
12-->[androidx.core:core:1.10.1] C:\Users\<USER>\.gradle\caches\transforms-3\cb397eff35725b147c51b001dccff3a5\transformed\core-1.10.1\AndroidManifest.xml:23:9-81
13        android:protectionLevel="signature" />
13-->[androidx.core:core:1.10.1] C:\Users\<USER>\.gradle\caches\transforms-3\cb397eff35725b147c51b001dccff3a5\transformed\core-1.10.1\AndroidManifest.xml:24:9-44
14
15    <uses-permission android:name="com.rootdetectionblocker.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION" />
15-->[androidx.core:core:1.10.1] C:\Users\<USER>\.gradle\caches\transforms-3\cb397eff35725b147c51b001dccff3a5\transformed\core-1.10.1\AndroidManifest.xml:26:5-97
15-->[androidx.core:core:1.10.1] C:\Users\<USER>\.gradle\caches\transforms-3\cb397eff35725b147c51b001dccff3a5\transformed\core-1.10.1\AndroidManifest.xml:26:22-94
16
17    <application
17-->D:\CData\Dsktp\lsposed_app\app\src\main\AndroidManifest.xml:5:5-36:19
18        android:allowBackup="true"
18-->D:\CData\Dsktp\lsposed_app\app\src\main\AndroidManifest.xml:6:9-35
19        android:appComponentFactory="androidx.core.app.CoreComponentFactory"
19-->[androidx.core:core:1.10.1] C:\Users\<USER>\.gradle\caches\transforms-3\cb397eff35725b147c51b001dccff3a5\transformed\core-1.10.1\AndroidManifest.xml:28:18-86
20        android:dataExtractionRules="@xml/data_extraction_rules"
20-->D:\CData\Dsktp\lsposed_app\app\src\main\AndroidManifest.xml:7:9-65
21        android:debuggable="true"
22        android:fullBackupContent="@xml/backup_rules"
22-->D:\CData\Dsktp\lsposed_app\app\src\main\AndroidManifest.xml:8:9-54
23        android:icon="@mipmap/ic_launcher"
23-->D:\CData\Dsktp\lsposed_app\app\src\main\AndroidManifest.xml:9:9-43
24        android:label="@string/app_name"
24-->D:\CData\Dsktp\lsposed_app\app\src\main\AndroidManifest.xml:10:9-41
25        android:theme="@style/Theme.AppCompat.Light.DarkActionBar" >
25-->D:\CData\Dsktp\lsposed_app\app\src\main\AndroidManifest.xml:11:9-67
26
27        <!-- Xposed Module Configuration -->
28        <meta-data
28-->D:\CData\Dsktp\lsposed_app\app\src\main\AndroidManifest.xml:15:9-17:36
29            android:name="xposedmodule"
29-->D:\CData\Dsktp\lsposed_app\app\src\main\AndroidManifest.xml:16:13-40
30            android:value="true" />
30-->D:\CData\Dsktp\lsposed_app\app\src\main\AndroidManifest.xml:17:13-33
31        <meta-data
31-->D:\CData\Dsktp\lsposed_app\app\src\main\AndroidManifest.xml:18:9-20:74
32            android:name="xposeddescription"
32-->D:\CData\Dsktp\lsposed_app\app\src\main\AndroidManifest.xml:19:13-45
33            android:value="Automatically blocks root detection popups" />
33-->D:\CData\Dsktp\lsposed_app\app\src\main\AndroidManifest.xml:20:13-71
34        <meta-data
34-->D:\CData\Dsktp\lsposed_app\app\src\main\AndroidManifest.xml:21:9-23:34
35            android:name="xposedminversion"
35-->D:\CData\Dsktp\lsposed_app\app\src\main\AndroidManifest.xml:22:13-44
36            android:value="54" />
36-->D:\CData\Dsktp\lsposed_app\app\src\main\AndroidManifest.xml:23:13-31
37
38        <!-- Main Activity (Optional - for module settings) -->
39        <activity
39-->D:\CData\Dsktp\lsposed_app\app\src\main\AndroidManifest.xml:26:9-34:20
40            android:name="com.rootdetectionblocker.MainActivity"
40-->D:\CData\Dsktp\lsposed_app\app\src\main\AndroidManifest.xml:27:13-41
41            android:exported="true"
41-->D:\CData\Dsktp\lsposed_app\app\src\main\AndroidManifest.xml:28:13-36
42            android:label="@string/app_name" >
42-->D:\CData\Dsktp\lsposed_app\app\src\main\AndroidManifest.xml:29:13-45
43            <intent-filter>
43-->D:\CData\Dsktp\lsposed_app\app\src\main\AndroidManifest.xml:30:13-33:29
44                <action android:name="android.intent.action.MAIN" />
44-->D:\CData\Dsktp\lsposed_app\app\src\main\AndroidManifest.xml:31:17-69
44-->D:\CData\Dsktp\lsposed_app\app\src\main\AndroidManifest.xml:31:25-66
45
46                <category android:name="android.intent.category.LAUNCHER" />
46-->D:\CData\Dsktp\lsposed_app\app\src\main\AndroidManifest.xml:32:17-77
46-->D:\CData\Dsktp\lsposed_app\app\src\main\AndroidManifest.xml:32:27-74
47            </intent-filter>
48        </activity>
49
50        <provider
50-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\5dcea79385f2c028e21d3b49c2fd53ce\transformed\jetified-emoji2-1.2.0\AndroidManifest.xml:24:9-32:20
51            android:name="androidx.startup.InitializationProvider"
51-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\5dcea79385f2c028e21d3b49c2fd53ce\transformed\jetified-emoji2-1.2.0\AndroidManifest.xml:25:13-67
52            android:authorities="com.rootdetectionblocker.androidx-startup"
52-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\5dcea79385f2c028e21d3b49c2fd53ce\transformed\jetified-emoji2-1.2.0\AndroidManifest.xml:26:13-68
53            android:exported="false" >
53-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\5dcea79385f2c028e21d3b49c2fd53ce\transformed\jetified-emoji2-1.2.0\AndroidManifest.xml:27:13-37
54            <meta-data
54-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\5dcea79385f2c028e21d3b49c2fd53ce\transformed\jetified-emoji2-1.2.0\AndroidManifest.xml:29:13-31:52
55                android:name="androidx.emoji2.text.EmojiCompatInitializer"
55-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\5dcea79385f2c028e21d3b49c2fd53ce\transformed\jetified-emoji2-1.2.0\AndroidManifest.xml:30:17-75
56                android:value="androidx.startup" />
56-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\5dcea79385f2c028e21d3b49c2fd53ce\transformed\jetified-emoji2-1.2.0\AndroidManifest.xml:31:17-49
57            <meta-data
57-->[androidx.lifecycle:lifecycle-process:2.4.1] C:\Users\<USER>\.gradle\caches\transforms-3\94430dc13316aeda0f68e85c4f4b7237\transformed\jetified-lifecycle-process-2.4.1\AndroidManifest.xml:31:13-33:52
58                android:name="androidx.lifecycle.ProcessLifecycleInitializer"
58-->[androidx.lifecycle:lifecycle-process:2.4.1] C:\Users\<USER>\.gradle\caches\transforms-3\94430dc13316aeda0f68e85c4f4b7237\transformed\jetified-lifecycle-process-2.4.1\AndroidManifest.xml:32:17-78
59                android:value="androidx.startup" />
59-->[androidx.lifecycle:lifecycle-process:2.4.1] C:\Users\<USER>\.gradle\caches\transforms-3\94430dc13316aeda0f68e85c4f4b7237\transformed\jetified-lifecycle-process-2.4.1\AndroidManifest.xml:33:17-49
60        </provider>
61    </application>
62
63</manifest>

package com.rootdetectionblocker

import android.os.Bundle
import android.widget.TextView
import androidx.appcompat.app.AppCompatActivity

class MainActivity : AppCompatActivity() {

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        
        // Create a simple layout programmatically
        val textView = TextView(this).apply {
            text = """
                Root Detection Blocker
                
                This Xposed module automatically blocks popups that contain both "rooting" and "detected" keywords.
                
                To use this module:
                1. Install this APK
                2. Enable it in Xposed Framework
                3. Reboot your device
                
                The module will work silently in the background.
            """.trimIndent()
            textSize = 16f
            setPadding(32, 32, 32, 32)
        }
        
        setContentView(textView)
    }
}

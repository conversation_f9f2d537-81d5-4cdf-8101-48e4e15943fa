{"logs": [{"outputFile": "com.rootdetectionblocker.app-mergeDebugResources-27:/values-ky/values-ky.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\730d661b65f3ba2f8a248144c0a9483c\\transformed\\appcompat-1.6.1\\res\\values-ky\\values-ky.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,216,325,437,522,627,744,823,901,992,1085,1180,1274,1374,1467,1562,1657,1748,1839,1920,2026,2131,2229,2336,2439,2554,2715,2817", "endColumns": "110,108,111,84,104,116,78,77,90,92,94,93,99,92,94,94,90,90,80,105,104,97,106,102,114,160,101,81", "endOffsets": "211,320,432,517,622,739,818,896,987,1080,1175,1269,1369,1462,1557,1652,1743,1834,1915,2021,2126,2224,2331,2434,2549,2710,2812,2894"}, "to": {"startLines": "6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,102", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "315,426,535,647,732,837,954,1033,1111,1202,1295,1390,1484,1584,1677,1772,1867,1958,2049,2130,2236,2341,2439,2546,2649,2764,2925,8758", "endColumns": "110,108,111,84,104,116,78,77,90,92,94,93,99,92,94,94,90,90,80,105,104,97,106,102,114,160,101,81", "endOffsets": "421,530,642,727,832,949,1028,1106,1197,1290,1385,1479,1579,1672,1767,1862,1953,2044,2125,2231,2336,2434,2541,2644,2759,2920,3022,8835"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\cb397eff35725b147c51b001dccff3a5\\transformed\\core-1.10.1\\res\\values-ky\\values-ky.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,155,257,360,467,571,675,786", "endColumns": "99,101,102,106,103,103,110,100", "endOffsets": "150,252,355,462,566,670,781,882"}, "to": {"startLines": "38,39,40,41,42,43,44,103", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3505,3605,3707,3810,3917,4021,4125,8840", "endColumns": "99,101,102,106,103,103,110,100", "endOffsets": "3600,3702,3805,3912,4016,4120,4231,8936"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\7f7393ba31e25e2f4376a79e2eb9d0b4\\transformed\\material-1.9.0\\res\\values-ky\\values-ky.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,265,348,433,518,633,743,844,985,1069,1133,1227,1297,1358,1445,1508,1572,1631,1705,1767,1821,1938,1996,2057,2111,2185,2307,2391,2487,2619,2697,2775,2864,2925,2980,3046,3115,3192,3279,3351,3427,3509,3582,3667,3746,3836,3928,4002,4087,4177,4229,4294,4379,4464,4526,4590,4653,4770,4864,4964,5059,5124,5183", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67", "endColumns": "12,82,84,84,114,109,100,140,83,63,93,69,60,86,62,63,58,73,61,53,116,57,60,53,73,121,83,95,131,77,77,88,60,54,65,68,76,86,71,75,81,72,84,78,89,91,73,84,89,51,64,84,84,61,63,62,116,93,99,94,64,58,81", "endOffsets": "260,343,428,513,628,738,839,980,1064,1128,1222,1292,1353,1440,1503,1567,1626,1700,1762,1816,1933,1991,2052,2106,2180,2302,2386,2482,2614,2692,2770,2859,2920,2975,3041,3110,3187,3274,3346,3422,3504,3577,3662,3741,3831,3923,3997,4082,4172,4224,4289,4374,4459,4521,4585,4648,4765,4859,4959,5054,5119,5178,5260"}, "to": {"startLines": "2,33,34,35,36,37,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,3027,3110,3195,3280,3395,4236,4337,4478,4562,4626,4720,4790,4851,4938,5001,5065,5124,5198,5260,5314,5431,5489,5550,5604,5678,5800,5884,5980,6112,6190,6268,6357,6418,6473,6539,6608,6685,6772,6844,6920,7002,7075,7160,7239,7329,7421,7495,7580,7670,7722,7787,7872,7957,8019,8083,8146,8263,8357,8457,8552,8617,8676", "endLines": "5,33,34,35,36,37,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101", "endColumns": "12,82,84,84,114,109,100,140,83,63,93,69,60,86,62,63,58,73,61,53,116,57,60,53,73,121,83,95,131,77,77,88,60,54,65,68,76,86,71,75,81,72,84,78,89,91,73,84,89,51,64,84,84,61,63,62,116,93,99,94,64,58,81", "endOffsets": "310,3105,3190,3275,3390,3500,4332,4473,4557,4621,4715,4785,4846,4933,4996,5060,5119,5193,5255,5309,5426,5484,5545,5599,5673,5795,5879,5975,6107,6185,6263,6352,6413,6468,6534,6603,6680,6767,6839,6915,6997,7070,7155,7234,7324,7416,7490,7575,7665,7717,7782,7867,7952,8014,8078,8141,8258,8352,8452,8547,8612,8671,8753"}}]}]}